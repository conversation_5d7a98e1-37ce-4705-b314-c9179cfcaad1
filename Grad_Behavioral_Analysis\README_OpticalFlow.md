# Optical Flow and Advanced Preprocessing for Behavioral Analysis

This document explains the new optical flow techniques and advanced preprocessing methods added to your behavioral analysis detection project.

## New Features Added

### 1. Optical Flow Techniques (helper.py)

#### Available Optical Flow Methods:
- **Farneback Method**: Dense optical flow using polynomial expansion
- **Lucas-Kanade Method**: Sparse optical flow for feature tracking
- **TV-L1 Method**: More accurate but slower dense optical flow

#### Key Functions:
```python
# Compute optical flow between two frames
flow = compute_optical_flow_farneback(frame1, frame2)

# Load frames with optical flow computation
frames, flows, flow_features, labels = load_all_class_frames_with_optical_flow(
    max_frames=100,
    flow_method='farneback'
)

# Create different dataset combinations
dataset = create_optical_flow_dataset(frames, flows, flow_features, method='concatenate')
```

### 2. Advanced Preprocessing Techniques (frames.py)

#### New Preprocessing Methods:
- **CLAHE**: Contrast Limited Adaptive Histogram Equalization
- **Gamma Correction**: Brightness adjustment
- **Unsharp Masking**: Edge enhancement
- **Bilateral Filtering**: Noise reduction while preserving edges
- **Morphological Operations**: Opening, closing, gradient, etc.
- **Edge Enhancement**: Sobel, Canny, Laplacian, Scharr
- **Histogram Stretching**: Contrast enhancement
- **Local Binary Patterns**: Texture analysis
- **Multi-scale Analysis**: Gaussian and Laplacian pyramids

#### Key Functions:
```python
# Apply advanced preprocessing pipeline
processed_frame = advanced_preprocess_frame(
    frame, 
    techniques=['grayscale', 'clahe', 'bilateral', 'resize', 'normalize']
)

# Create multi-channel representation
multi_channel = create_multi_channel_frame(
    frame, 
    channels=['original', 'edges', 'lbp']
)
```

### 3. Temporal Processing Techniques

#### Temporal Analysis Methods:
- **Temporal Difference**: Motion detection between frames
- **Motion History Images**: Accumulated motion over time
- **Temporal Smoothing**: Noise reduction across time
- **Adaptive Frame Sampling**: Intelligent frame selection
- **Temporal Features**: Statistical analysis across time

#### Key Functions:
```python
# Extract motion between frames
motion_frames = temporal_difference(frames, method='adjacent')

# Create motion history image
mhi = extract_motion_history(frames, duration=5)

# Sample frames based on content
sampled_frames, sampled_labels = adaptive_frame_sampling(
    frames, labels, target_frames=10, method='motion_based'
)
```

## Installation Requirements

Make sure you have the following packages installed:

```bash
pip install opencv-python
pip install scikit-image
pip install scipy
pip install numpy
pip install matplotlib
pip install tensorflow
pip install scikit-learn
```

For TV-L1 optical flow (optional):
```bash
pip install opencv-contrib-python
```

## Usage Examples

### Basic Optical Flow Usage

```python
from helper import load_all_class_frames_with_optical_flow, create_optical_flow_dataset
from config import CATEGORIES, DATA_PATH

# Load data with optical flow
frames, flows, flow_features, labels = load_all_class_frames_with_optical_flow(
    max_frames=100,
    CATEGORIES=CATEGORIES,
    DATA_PATH=DATA_PATH,
    flow_method='farneback'
)

# Create concatenated dataset (frames + flow magnitude)
combined_data = create_optical_flow_dataset(
    frames, flows, flow_features, method='concatenate'
)

# Now combined_data has shape (N, H, W, 2) - original + flow magnitude
```

### Advanced Preprocessing Usage

```python
from frames import advanced_preprocess_frame

# Define preprocessing pipeline
preprocessing_techniques = [
    'grayscale',    # Convert to grayscale
    'clahe',        # Apply CLAHE
    'bilateral',    # Bilateral filtering
    'sharpen',      # Unsharp masking
    'resize',       # Resize to target size
    'normalize'     # Normalize to [0,1]
]

# Apply to a frame
processed = advanced_preprocess_frame(frame, techniques=preprocessing_techniques)
```

### Temporal Processing Usage

```python
from frames import temporal_difference, extract_motion_history, create_temporal_features

# Extract motion between consecutive frames
motion_frames = temporal_difference(video_frames, method='adjacent')

# Create motion history image
mhi = extract_motion_history(video_frames, duration=10, threshold=30)

# Extract temporal statistics
temp_stats = create_temporal_features(video_frames, feature_type='statistics')
```

## Integration with Existing Models

### 1. CNN Model Integration

```python
# Modify your CNN to accept multi-channel input
def build_cnn_with_optical_flow():
    model = models.Sequential([
        layers.Input(shape=(IMG_SIZE, IMG_SIZE, 2)),  # Frame + Flow magnitude
        layers.Conv2D(32, 3, activation='relu', padding='same'),
        layers.MaxPooling2D(),
        # ... rest of your CNN architecture
    ])
    return model
```

### 2. Multi-Input Model

```python
def build_multi_input_model():
    # Frame input
    frame_input = layers.Input(shape=(IMG_SIZE, IMG_SIZE, 1), name='frames')
    frame_features = layers.Conv2D(32, 3, activation='relu')(frame_input)
    
    # Flow input
    flow_input = layers.Input(shape=(IMG_SIZE, IMG_SIZE, 2), name='flows')
    flow_features = layers.Conv2D(32, 3, activation='relu')(flow_input)
    
    # Combine features
    combined = layers.concatenate([frame_features, flow_features])
    
    # Continue with your architecture
    # ...
    
    model = models.Model(inputs=[frame_input, flow_input], outputs=output)
    return model
```

### 3. Feature-Based Model

```python
def build_feature_based_model():
    # Use extracted optical flow features
    feature_input = layers.Input(shape=(9,))  # 9 optical flow features
    dense = layers.Dense(64, activation='relu')(feature_input)
    # ... rest of your architecture
    
    return model
```

## Performance Tips

1. **Optical Flow Method Selection**:
   - Use `farneback` for general purpose (good balance of speed/accuracy)
   - Use `lucas_kanade` for sparse tracking applications
   - Use `tvl1` for highest accuracy (but slower)

2. **Preprocessing Pipeline**:
   - Start with basic techniques: `['grayscale', 'clahe', 'resize', 'normalize']`
   - Add more techniques based on your specific data characteristics
   - Monitor processing time vs. accuracy improvements

3. **Memory Management**:
   - Process videos in batches to avoid memory issues
   - Use `max_frames_per_video` parameter to limit memory usage
   - Consider using generators for large datasets

## Running the Demo

To see the new features in action:

```bash
cd Grad_Behavioral_Analysis
python example_optical_flow.py
```

This will demonstrate:
- Optical flow computation and visualization
- Advanced preprocessing techniques
- Dataset creation methods
- Temporal processing capabilities

## Next Steps

1. **Experiment with different optical flow methods** on your specific dataset
2. **Combine multiple preprocessing techniques** to find the best pipeline
3. **Integrate optical flow features** into your existing CNN/LSTM models
4. **Use temporal processing** for better motion understanding
5. **Create multi-input models** that leverage both spatial and temporal information

## Troubleshooting

- **Import errors**: Make sure all required packages are installed
- **Memory issues**: Reduce `max_frames_per_video` or process in smaller batches
- **Slow processing**: Use `farneback` instead of `tvl1` for faster processing
- **Poor optical flow quality**: Adjust parameters or try different methods

For more specific help, check the individual function documentation in the code files.
