import os
import numpy as np
import tensorflow as tf
from tensorflow.keras import layers, models
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt
import pandas as pd
from helper import load_all_class_frames
from config import VIT_RESULTS_DIR, DATA_PATH, SKIP, CATEGORIES, IMG_SIZE, NUM_FRAMES


def group_frames_into_clips(frames, labels, num_frames):
    """Group frames into clips of num_frames for video classification."""
    clips = []
    clip_labels = []
    for i in range(0, len(frames) - num_frames + 1, num_frames):
        clip = frames[i : i + num_frames]
        if len(clip) == num_frames:
            clips.append(clip)
            clip_labels.append(
                labels[i]
            )  # Use the label of the first frame in the clip
    return np.array(clips), np.array(clip_labels)


class PatchEmbedding(layers.Layer):
    def __init__(self, patch_size=16, embed_dim=768):
        super(PatchEmbedding, self).__init__()
        self.patch_size = patch_size
        self.embed_dim = embed_dim
        self.num_patches = (IMG_SIZE // patch_size) ** 2
        self.proj = layers.Conv2D(
            filters=embed_dim,
            kernel_size=patch_size,
            strides=patch_size,
            padding="valid",
        )
        # Initialize pos_embed and cls_token with correct add_weight syntax
        self.pos_embed = self.add_weight(
            name="pos_embed",
            shape=(1, NUM_FRAMES * self.num_patches + 1, embed_dim),
            initializer="random_normal",
            trainable=True,
        )
        self.cls_token = self.add_weight(
            name="cls_token",
            shape=(1, 1, embed_dim),
            initializer="random_normal",
            trainable=True,
        )

    def call(self, x):
        # x: (batch_size, NUM_FRAMES, IMG_SIZE, IMG_SIZE, 1)
        batch_size = tf.shape(x)[0]
        frames = tf.split(
            x, NUM_FRAMES, axis=1
        )  # List of (batch_size, 1, IMG_SIZE, IMG_SIZE, 1)
        frames = [
            tf.squeeze(frame, axis=1) for frame in frames
        ]  # (batch_size, IMG_SIZE, IMG_SIZE, 1)

        # Extract patches for each frame
        patches = []
        for frame in frames:
            frame_patches = self.proj(frame)  # (batch_size, h, w, embed_dim)
            frame_patches = tf.reshape(
                frame_patches, [batch_size, self.num_patches, self.embed_dim]
            )
            patches.append(frame_patches)

        # Concatenate patches across frames
        x = tf.concat(
            patches, axis=1
        )  # (batch_size, NUM_FRAMES * num_patches, embed_dim)

        # Add CLS token
        cls_tokens = tf.tile(
            self.cls_token, [batch_size, 1, 1]
        )  # (batch_size, 1, embed_dim)
        x = tf.concat(
            [cls_tokens, x], axis=1
        )  # (batch_size, NUM_FRAMES * num_patches + 1, embed_dim)

        # Add positional embedding
        x = x + self.pos_embed
        return x


class TransformerEncoder(layers.Layer):
    def __init__(self, embed_dim=768, num_heads=8, mlp_dim=3072, dropout=0.1):
        super(TransformerEncoder, self).__init__()
        self.norm1 = layers.LayerNormalization(epsilon=1e-6)
        self.attn = layers.MultiHeadAttention(
            num_heads=num_heads, key_dim=embed_dim // num_heads
        )
        self.norm2 = layers.LayerNormalization(epsilon=1e-6)
        self.mlp = models.Sequential(
            [
                layers.Dense(mlp_dim, activation="gelu"),
                layers.Dropout(dropout),
                layers.Dense(embed_dim),
                layers.Dropout(dropout),
            ]
        )

    def call(self, x, training=False):
        x = x + self.attn(self.norm1(x), self.norm1(x), training=training)
        x = x + self.mlp(self.norm2(x), training=training)
        return x


def build_classifier():
    inputs = layers.Input(shape=(NUM_FRAMES, IMG_SIZE, IMG_SIZE, 1))

    # Patch embedding
    x = PatchEmbedding(patch_size=16, embed_dim=768)(inputs)

    # Transformer encoder layers
    for _ in range(4):  # Reduced depth for computational efficiency
        x = TransformerEncoder(embed_dim=768, num_heads=8, mlp_dim=3072, dropout=0.1)(
            x, training=True
        )

    # Final classification
    x = layers.LayerNormalization(epsilon=1e-6)(x)
    cls_token = x[:, 0]  # Extract CLS token
    outputs = layers.Dense(len(CATEGORIES), activation="softmax")(cls_token)

    model = models.Model(inputs, outputs)
    model.compile(
        optimizer="adam", loss="sparse_categorical_crossentropy", metrics=["accuracy"]
    )
    return model


# Load video data
clf_data, clf_labels = load_all_class_frames(
    max_frames=2500, CATEGORIES=CATEGORIES, DATA_PATH=DATA_PATH, skip=SKIP
)

# Debug: Print data shapes
print(f"clf_data shape: {clf_data.shape}")
print(f"clf_labels shape: {clf_labels.shape}")

# Group frames into clips
clf_data, clf_labels = group_frames_into_clips(clf_data, clf_labels, NUM_FRAMES)
print(f"Clipped clf_data shape: {clf_data.shape}")
print(f"Clipped clf_labels shape: {clf_labels.shape}")

# Split data
x_clf_train, x_clf_test, y_clf_train, y_clf_test = train_test_split(
    clf_data, clf_labels, test_size=0.2, random_state=42
)
print(f"x_clf_train shape: {x_clf_train.shape}")
print(f"x_clf_test shape: {x_clf_test.shape}")

classifier = build_classifier()
print(classifier.summary())

classifier.fit(
    x_clf_train,
    y_clf_train,
    epochs=10,
    batch_size=32,
    validation_data=(x_clf_test, y_clf_test),
)

y_pred = np.argmax(classifier.predict(x_clf_test), axis=1)
print(classification_report(y_clf_test, y_pred, target_names=CATEGORIES))

# Create results directory and save model
os.makedirs(VIT_RESULTS_DIR, exist_ok=True)
classifier.save(f"{VIT_RESULTS_DIR}/video_vit_classifier_model.h5")

# Save confusion matrix
y_true = np.asarray(y_clf_test).ravel()
y_pred_arr = np.asarray(y_pred).ravel()
cm = confusion_matrix(y_true, y_pred_arr)
np.save(f"{VIT_RESULTS_DIR}/confusion_matrix_vit.npy", cm)

plt.figure(figsize=(10, 8))
plt.imshow(cm, interpolation="nearest", cmap=plt.cm.Blues)
plt.title("Confusion Matrix")
plt.colorbar()
tick_marks = np.arange(len(CATEGORIES))
plt.xticks(tick_marks, CATEGORIES, rotation=45)
plt.yticks(tick_marks, CATEGORIES)
plt.ylabel("True label")
plt.xlabel("Predicted label")
plt.tight_layout()
plt.savefig(f"{VIT_RESULTS_DIR}/confusion_matrix_vit.png")
plt.close()

# Save classification report
report = classification_report(
    y_clf_test, y_pred, target_names=CATEGORIES, output_dict=True
)
df = pd.DataFrame(report).transpose()
df.to_csv(f"{VIT_RESULTS_DIR}/classification_report_vit.csv")

# Evaluate the model
loss, accuracy = classifier.evaluate(x_clf_test, y_clf_test)
print(f"Test Loss: {loss:.4f}")
print(f"Test Accuracy: {accuracy:.4f}")
