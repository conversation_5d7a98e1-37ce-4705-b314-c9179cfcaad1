import os

IMG_SIZE = 128
DATA_PATH = "../Grad/data/data/abnormal class"
CATEGORIES = ["Arrest", "Attack", "Burglary", "Explosion", "Fighting"]
CATEGORIES = ["Arrest", "Attack"]
CATEGORIES = ["Arrest"]


def getCategories(DATA_PATH=DATA_PATH, full=False):
    if not full:
        return CATEGORIES
    return [
        d for d in os.listdir(DATA_PATH) if os.path.isdir(os.path.join(DATA_PATH, d))
    ]


# CATEGORIES = getCategories(full=True)
SKIP = 5
EPOCHS = 10
MAX_FRAMES = 2500
CNN_RESULTS_DIR = "Results/CNN"
CNN3D_RESULTS_DIR = "Results/Conv3D"
VIT_RESULTS_DIR = "Results/VIT"
CNN_LSTM_RESULTS_DIR = "Results/CNN_LSTM"
SWIN_RESULTS_DIR = "Results/SwinTransformer"
NUM_FRAMES = 4
BATCH_SIZE = 32

# Reports
CM = "confusion_matrix"
CR = "classification_report"
EVALUATION = "evaluation"
REPORTS = [CM, CR, EVALUATION]

# processing frame
RESIZE = "resize"
EQUALIZE = "equalize"
CLAHE = "clahe"
BILATERAL = "bilateral"
SHARPEN = "sharpen"
GAMMA = "gamma"
STRETCH = "stretch"
MORPH_OPEN = "morph_open"
MORPH_CLOSE = "morph_close"
EDGES = "edges"
NORMALIZE = "normalize"
GRAYSCALE = "grayscale"

# Basic processing modes
PROCESSING = [RESIZE, EQUALIZE]

# Advanced processing modes
ADVANCED_PROCESSING = [GRAYSCALE, CLAHE, BILATERAL, RESIZE, NORMALIZE]

# Optical flow methods
OPTICAL_FLOW_FARNEBACK = "farneback"
OPTICAL_FLOW_LUCAS_KANADE = "lucas_kanade"
OPTICAL_FLOW_TVL1 = "tvl1"

# Optical flow dataset creation methods
FLOW_CONCATENATE = "concatenate"
FLOW_SEPARATE = "separate"
FLOW_MAGNITUDE_ONLY = "magnitude_only"
FLOW_FEATURES_ONLY = "features_only"

# Temporal processing methods
TEMPORAL_ADJACENT = "adjacent"
TEMPORAL_BACKGROUND = "background_subtraction"
TEMPORAL_GAUSSIAN = "gaussian"
TEMPORAL_MEDIAN = "median"
TEMPORAL_MEAN = "mean"

# Frame sampling methods
SAMPLING_UNIFORM = "uniform"
SAMPLING_MOTION = "motion_based"
SAMPLING_ENTROPY = "entropy_based"
