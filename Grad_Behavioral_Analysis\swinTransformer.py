import os
import cv2
import numpy as np
import tensorflow as tf
from tensorflow.keras import layers, models
from tensorflow.keras.callbacks import ModelCheckpoint
from sklearn.model_selection import train_test_split
from helper import load_all_class_frames, evaluateModel, saveBestModel, saveLastModel
from config import (
    SWIN_RESULTS_DIR,
    DATA_PATH,
    SKIP,
    CATEGORIES,
    IMG_SIZE,
    MAX_FRAMES,
)


class WindowAttention(layers.Layer):
    def __init__(self, dim, window_size, num_heads):
        super().__init__()
        self.dim = dim
        self.window_size = window_size
        self.num_heads = num_heads
        self.scale = (dim // num_heads) ** -0.5
        self.qkv = layers.Dense(dim * 3, use_bias=False)
        self.proj = layers.Dense(dim)

    def build(self, input_shape):
        self.qkv.build(input_shape)
        self.proj.build(input_shape)
        super().build(input_shape)

    def call(self, x):
        B, H, W, C = tf.shape(x)[0], tf.shape(x)[1], tf.shape(x)[2], tf.shape(x)[3]
        qkv = self.qkv(x)
        qkv = tf.reshape(qkv, [B, H, W, 3, self.num_heads, C // self.num_heads])
        qkv = tf.transpose(qkv, [3, 0, 4, 1, 2, 5])
        q, k, v = qkv[0], qkv[1], qkv[2]
        attn = tf.matmul(q, k, transpose_b=True) * self.scale
        attn = tf.nn.softmax(attn, axis=-1)
        x = tf.matmul(attn, v)
        x = tf.transpose(x, [0, 2, 3, 1, 4])
        x = tf.reshape(x, [B, H, W, C])
        return self.proj(x)


class SwinBlock(layers.Layer):
    def __init__(self, dim, window_size, num_heads, shift_size=0):
        super().__init__()
        self.norm1 = layers.LayerNormalization(epsilon=1e-5)
        self.attn = WindowAttention(dim, window_size, num_heads)
        self.norm2 = layers.LayerNormalization(epsilon=1e-5)
        self.mlp = models.Sequential(
            [
                layers.Dense(dim * 4, activation="gelu"),
                layers.Dense(dim),
            ]
        )
        self.shift_size = shift_size

    def build(self, input_shape):
        self.norm1.build(input_shape)
        self.attn.build(input_shape)
        self.norm2.build(input_shape)
        self.mlp.build(input_shape)
        super().build(input_shape)

    def call(self, x):
        # Simplified: assume no shift for now
        shortcut = x
        x = self.norm1(x)
        x = self.attn(x)
        x = shortcut + x
        shortcut = x
        x = self.norm2(x)
        x = self.mlp(x)
        return shortcut + x


def build_classifier():
    inputs = layers.Input(shape=(IMG_SIZE, IMG_SIZE, 1))
    x = layers.Conv2D(96, 4, strides=4, padding="valid")(inputs)  # Patch embedding
    x = SwinBlock(96, window_size=7, num_heads=3)(x)
    x = SwinBlock(96, window_size=7, num_heads=3, shift_size=3)(x)
    x = layers.GlobalAveragePooling2D()(x)
    x = layers.Dense(128, activation="relu")(x)
    x = layers.Dropout(0.3)(x)
    outputs = layers.Dense(len(CATEGORIES), activation="softmax")(x)
    model = models.Model(inputs, outputs)
    model.compile(
        optimizer="adam", loss="sparse_categorical_crossentropy", metrics=["accuracy"]
    )
    return model


clf_data, clf_labels = load_all_class_frames(
    max_frames=MAX_FRAMES, CATEGORIES=CATEGORIES, DATA_PATH=DATA_PATH, skip=SKIP
)
x_clf_train, x_clf_test, y_clf_train, y_clf_test = train_test_split(
    clf_data, clf_labels, test_size=0.2, random_state=42
)

classifier = build_classifier()
print(classifier.summary())

os.makedirs(SWIN_RESULTS_DIR, exist_ok=True)

classifier.fit(
    x_clf_train,
    y_clf_train,
    epochs=10,
    batch_size=32,
    validation_data=(x_clf_test, y_clf_test),
    callbacks=[
        saveBestModel(f"{SWIN_RESULTS_DIR}/best_model_SwinTransformer.h5"),
        saveLastModel(f"{SWIN_RESULTS_DIR}/last_model_SwinTransformer.h5"),
    ],
)

evaluateModel(classifier, x_clf_test, y_clf_test, SWIN_RESULTS_DIR)
