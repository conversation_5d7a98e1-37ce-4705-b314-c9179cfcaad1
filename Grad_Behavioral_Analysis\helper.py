import os
import cv2
import numpy as np
from sklearn.metrics import classification_report, confusion_matrix
from tensorflow.keras.callbacks import ModelCheckpoint
from config import DATA_PATH, CATEGORIES, IMG_SIZE, CM, CR, EVALUATION
from reports import evaluate, makeClassificationReport, makeConfusionMatrix
from frames import preprocess_frame


# ============================================================================
# OPTICAL FLOW FUNCTIONS
# ============================================================================

def compute_optical_flow_farneback(frame1, frame2, **kwargs):
    """
    Compute dense optical flow using Farneback method.

    Args:
        frame1: Previous frame (grayscale)
        frame2: Current frame (grayscale)
        **kwargs: Additional parameters for cv2.calcOpticalFlowPyrLK

    Returns:
        flow: Optical flow field (H, W, 2)
    """
    # Default parameters for Farneback
    params = {
        'pyr_scale': 0.5,
        'levels': 3,
        'winsize': 15,
        'iterations': 3,
        'poly_n': 5,
        'poly_sigma': 1.2,
        'flags': 0
    }
    params.update(kwargs)

    flow = cv2.calcOpticalFlowPyrLK(frame1, frame2, **params)
    return flow


def compute_optical_flow_lucas_kanade(frame1, frame2, corners=None, **kwargs):
    """
    Compute sparse optical flow using Lucas-Kanade method.

    Args:
        frame1: Previous frame (grayscale)
        frame2: Current frame (grayscale)
        corners: Feature points to track (if None, will detect automatically)
        **kwargs: Additional parameters

    Returns:
        new_corners: New positions of tracked points
        status: Status of each point (1 if found, 0 if not)
        error: Error for each point
    """
    if corners is None:
        # Detect corners using goodFeaturesToTrack
        corners = cv2.goodFeaturesToTrack(
            frame1,
            maxCorners=kwargs.get('maxCorners', 100),
            qualityLevel=kwargs.get('qualityLevel', 0.3),
            minDistance=kwargs.get('minDistance', 7),
            blockSize=kwargs.get('blockSize', 7)
        )

    if corners is None or len(corners) == 0:
        return None, None, None

    # Parameters for Lucas-Kanade
    lk_params = {
        'winSize': (kwargs.get('winSize', 15), kwargs.get('winSize', 15)),
        'maxLevel': kwargs.get('maxLevel', 2),
        'criteria': (cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 10, 0.03)
    }

    new_corners, status, error = cv2.calcOpticalFlowPyrLK(
        frame1, frame2, corners, None, **lk_params
    )

    return new_corners, status, error


def compute_optical_flow_tvl1(frame1, frame2, **kwargs):
    """
    Compute dense optical flow using TV-L1 method (more accurate but slower).

    Args:
        frame1: Previous frame (grayscale)
        frame2: Current frame (grayscale)
        **kwargs: Additional parameters

    Returns:
        flow: Optical flow field (H, W, 2)
    """
    try:
        # Create TV-L1 optical flow object
        tvl1 = cv2.optflow.DualTVL1OpticalFlow_create()

        # Set parameters if provided
        if 'tau' in kwargs:
            tvl1.setTau(kwargs['tau'])
        if 'lambda' in kwargs:
            tvl1.setLambda(kwargs['lambda'])
        if 'theta' in kwargs:
            tvl1.setTheta(kwargs['theta'])
        if 'nscales' in kwargs:
            tvl1.setNumScales(kwargs['nscales'])
        if 'warps' in kwargs:
            tvl1.setNumWarps(kwargs['warps'])
        if 'epsilon' in kwargs:
            tvl1.setEpsilon(kwargs['epsilon'])
        if 'innnerIterations' in kwargs:
            tvl1.setInnerIterations(kwargs['innnerIterations'])
        if 'outerIterations' in kwargs:
            tvl1.setOuterIterations(kwargs['outerIterations'])

        flow = tvl1.calc(frame1, frame2, None)
        return flow
    except AttributeError:
        print("TV-L1 optical flow not available, falling back to Farneback")
        return compute_optical_flow_farneback(frame1, frame2, **kwargs)


def optical_flow_to_hsv(flow):
    """
    Convert optical flow to HSV color representation.

    Args:
        flow: Optical flow field (H, W, 2)

    Returns:
        hsv_image: HSV representation of optical flow (H, W, 3)
    """
    h, w = flow.shape[:2]
    hsv = np.zeros((h, w, 3), dtype=np.uint8)
    hsv[..., 1] = 255

    # Calculate magnitude and angle
    mag, ang = cv2.cartToPolar(flow[..., 0], flow[..., 1])
    hsv[..., 0] = ang * 180 / np.pi / 2
    hsv[..., 2] = cv2.normalize(mag, None, 0, 255, cv2.NORM_MINMAX)

    return hsv


def optical_flow_magnitude(flow):
    """
    Calculate magnitude of optical flow vectors.

    Args:
        flow: Optical flow field (H, W, 2)

    Returns:
        magnitude: Flow magnitude (H, W)
    """
    return np.sqrt(flow[..., 0]**2 + flow[..., 1]**2)


def extract_optical_flow_features(flow):
    """
    Extract statistical features from optical flow.

    Args:
        flow: Optical flow field (H, W, 2)

    Returns:
        features: Dictionary of extracted features
    """
    magnitude = optical_flow_magnitude(flow)

    features = {
        'mean_magnitude': np.mean(magnitude),
        'std_magnitude': np.std(magnitude),
        'max_magnitude': np.max(magnitude),
        'min_magnitude': np.min(magnitude),
        'mean_flow_x': np.mean(flow[..., 0]),
        'mean_flow_y': np.mean(flow[..., 1]),
        'std_flow_x': np.std(flow[..., 0]),
        'std_flow_y': np.std(flow[..., 1]),
        'flow_density': np.sum(magnitude > np.mean(magnitude)) / magnitude.size
    }

    return features


# Step 3: Extract frames from videos
def extract_frames_from_videos(folder, max_frames_per_video=10, skip=20):
    data = []

    # Basic input validation
    if not os.path.exists(folder):
        print(f"Folder not found: {folder}")
        return np.array(data)

    if max_frames_per_video <= 0:
        print("max_frames_per_video must be > 0")
        return np.array(data)

    if skip is None or skip < 0:
        print("Invalid skip value provided, using default skip=10")
        skip = 10

    try:
        files = os.listdir(folder)
    except Exception as e:
        print(f"Could not list folder {folder}: {e}")
        return np.array(data)

    print(f"Loading from {folder} ({len(files)} files)")

    for file in files:
        if not file.lower().endswith((".mp4", ".avi", ".mov", ".mkv")):
            continue
        cap = cv2.VideoCapture(os.path.join(folder, file))
        count = 0  # number of frames extracted for this video
        frame_idx = 0  # absolute frame index read from video
        while cap.isOpened() and (
            count < max_frames_per_video or max_frames_per_video == -1
        ):
            ret, frame = cap.read()
            if not ret:
                break

            # Take the frame if it's the sampling frame (every skip+1 frames)
            if frame_idx % (skip + 1) == 0:
                processed = preprocess_frame(frame)
                if processed is None:
                    frame_idx += 1
                    continue
                data.append(np.expand_dims(processed, -1))  # (H, W, 1)
                count += 1

            frame_idx += 1

        cap.release()

    print(f"Extracted {len(data)} frames from {folder}")
    return np.array(data)


def load_all_class_frames(
    max_frames=5, CATEGORIES=CATEGORIES, DATA_PATH=DATA_PATH, skip=20
):
    all_data = []
    all_labels = []
    label_map = {cat: idx for idx, cat in enumerate(CATEGORIES)}

    if CATEGORIES is None or len(CATEGORIES) == 0:
        # getting all the classes on the Data Path
        CATEGORIES = [
            d
            for d in os.listdir(DATA_PATH)
            if os.path.isdir(os.path.join(DATA_PATH, d))
        ]
        print(f"Detected categories: {CATEGORIES}")

    for cat in CATEGORIES:
        print(f"{cat}")
        folder = os.path.join(DATA_PATH, cat)
        frames = extract_frames_from_videos(
            folder, max_frames_per_video=max_frames, skip=skip
        )
        all_data.extend(frames)
        all_labels.extend([label_map[cat]] * len(frames))

    return np.array(all_data), np.array(all_labels)


def extract_frames_with_optical_flow(folder, max_frames_per_video=10, skip=20, flow_method='farneback'):
    """
    Extract frames along with their optical flow information.

    Args:
        folder: Path to video folder
        max_frames_per_video: Maximum frames to extract per video
        skip: Number of frames to skip between extractions
        flow_method: 'farneback', 'lucas_kanade', or 'tvl1'

    Returns:
        frames: Array of preprocessed frames
        flows: Array of optical flow data
        flow_features: Array of extracted flow features
    """
    frames = []
    flows = []
    flow_features = []

    # Basic input validation
    if not os.path.exists(folder):
        print(f"Folder not found: {folder}")
        return np.array(frames), np.array(flows), np.array(flow_features)

    if max_frames_per_video <= 0:
        print("max_frames_per_video must be > 0")
        return np.array(frames), np.array(flows), np.array(flow_features)

    if skip is None or skip < 0:
        print("Invalid skip value provided, using default skip=10")
        skip = 10

    try:
        files = os.listdir(folder)
    except Exception as e:
        print(f"Could not list folder {folder}: {e}")
        return np.array(frames), np.array(flows), np.array(flow_features)

    print(f"Loading from {folder} with optical flow ({len(files)} files)")

    for file in files:
        if not file.lower().endswith((".mp4", ".avi", ".mov", ".mkv")):
            continue

        cap = cv2.VideoCapture(os.path.join(folder, file))
        count = 0
        frame_idx = 0
        prev_frame = None

        while cap.isOpened() and (count < max_frames_per_video or max_frames_per_video == -1):
            ret, frame = cap.read()
            if not ret:
                break

            # Take the frame if it's the sampling frame
            if frame_idx % (skip + 1) == 0:
                processed = preprocess_frame(frame)
                if processed is None:
                    frame_idx += 1
                    continue

                # Convert to uint8 for optical flow computation
                current_frame = (processed * 255).astype(np.uint8)

                if prev_frame is not None:
                    # Compute optical flow
                    if flow_method == 'farneback':
                        flow = compute_optical_flow_farneback(prev_frame, current_frame)
                    elif flow_method == 'lucas_kanade':
                        # For Lucas-Kanade, we'll use dense flow approximation
                        flow = compute_optical_flow_farneback(prev_frame, current_frame)
                    elif flow_method == 'tvl1':
                        flow = compute_optical_flow_tvl1(prev_frame, current_frame)
                    else:
                        flow = compute_optical_flow_farneback(prev_frame, current_frame)

                    # Extract flow features
                    features = extract_optical_flow_features(flow)

                    # Store data
                    frames.append(np.expand_dims(processed, -1))
                    flows.append(flow)
                    flow_features.append(list(features.values()))
                    count += 1

                prev_frame = current_frame.copy()

            frame_idx += 1

        cap.release()

    print(f"Extracted {len(frames)} frames with optical flow from {folder}")
    return np.array(frames), np.array(flows), np.array(flow_features)


def load_all_class_frames_with_optical_flow(
    max_frames=5, CATEGORIES=CATEGORIES, DATA_PATH=DATA_PATH, skip=20, flow_method='farneback'
):
    """
    Load frames with optical flow for all classes.

    Returns:
        all_data: Array of frames
        all_flows: Array of optical flows
        all_flow_features: Array of flow features
        all_labels: Array of labels
    """
    all_data = []
    all_flows = []
    all_flow_features = []
    all_labels = []
    label_map = {cat: idx for idx, cat in enumerate(CATEGORIES)}

    if CATEGORIES is None or len(CATEGORIES) == 0:
        CATEGORIES = [
            d for d in os.listdir(DATA_PATH)
            if os.path.isdir(os.path.join(DATA_PATH, d))
        ]
        print(f"Detected categories: {CATEGORIES}")

    for cat in CATEGORIES:
        print(f"Processing {cat} with optical flow...")
        folder = os.path.join(DATA_PATH, cat)
        frames, flows, flow_features = extract_frames_with_optical_flow(
            folder, max_frames_per_video=max_frames, skip=skip, flow_method=flow_method
        )

        all_data.extend(frames)
        all_flows.extend(flows)
        all_flow_features.extend(flow_features)
        all_labels.extend([label_map[cat]] * len(frames))

    return (
        np.array(all_data),
        np.array(all_flows),
        np.array(all_flow_features),
        np.array(all_labels)
    )


def create_optical_flow_dataset(frames, flows, flow_features, method='concatenate'):
    """
    Create different types of datasets combining frames and optical flow.

    Args:
        frames: Array of frames (N, H, W, 1)
        flows: Array of optical flows (N, H, W, 2)
        flow_features: Array of flow features (N, num_features)
        method: 'concatenate', 'separate', 'magnitude_only', 'features_only'

    Returns:
        Combined dataset based on the specified method
    """
    if method == 'concatenate':
        # Concatenate frames with flow magnitude
        flow_mag = np.expand_dims(optical_flow_magnitude(flows), -1)
        combined = np.concatenate([frames, flow_mag], axis=-1)
        return combined

    elif method == 'separate':
        # Return frames and flows separately for multi-input models
        return frames, flows

    elif method == 'magnitude_only':
        # Use only optical flow magnitude
        flow_mag = np.expand_dims(optical_flow_magnitude(flows), -1)
        return flow_mag

    elif method == 'features_only':
        # Use only extracted flow features
        return flow_features

    else:
        raise ValueError(f"Unknown method: {method}")


def saveBestModel(saveName):
    best_checkpoint = ModelCheckpoint(
        filepath=saveName,
        monitor="val_accuracy",
        mode="max",
        save_best_only=True,
        verbose=1,
    )
    return best_checkpoint


def saveLastModel(saveName):
    last_checkpoint = ModelCheckpoint(filepath=saveName, save_freq="epoch", verbose=1)
    return last_checkpoint


def evaluateModel(
    model,
    testData,
    testLabels,
    DIR,
    batch_size=32,
    verbose=1,
    reports=[CM, CR, EVALUATION],
):
    for report in reports:
        if report == CM:
            makeConfusionMatrix(model, testData, testLabels, f"{DIR}")
        elif report == CR:
            makeClassificationReport(model, testData, testLabels, f"{DIR}")
        elif report == EVALUATION:
            evaluate(model, testData, testLabels, batch_size, verbose)
