import numpy as np
from sklearn.metrics import classification_report, confusion_matrix
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from config import CATEGORIES


def evaluate(model, testData, testLabels, batch_size=32, verbose=1):

    loss, accuracy = model.evaluate(
        testData, testLabels, batch_size=batch_size, verbose=verbose
    )
    print(f"Test Loss: {loss:.4f}, Test Accuracy: {accuracy:.4f}")
    return loss, accuracy


def makeClassificationReport(
    model, testData, testLabels, DIR, batch_size=32, verbose=1
):
    y_pred = np.argmax(model.predict(testData), axis=1)
    report = classification_report(
        testLabels, y_pred, target_names=CATEGORIES, output_dict=True
    )
    df = pd.DataFrame(report).transpose()
    df.to_csv(f"{DIR}/classification_report.csv")
    print(classification_report(testLabels, y_pred, target_names=CATEGORIES))

    return report


def makeConfusionMatrix(model, testData, testLabels, DIR):
    y_pred = np.argmax(model.predict(testData), axis=1)
    cm = confusion_matrix(testLabels, y_pred)
    np.save(f"{DIR}/confusion_matrix.npy", cm)

    plt.figure(figsize=(10, 8))
    sns.heatmap(
        cm,
        annot=True,  # Show numbers in cells
        fmt="d",  # Integer format for cell values
        cmap="Blues",
        xticklabels=CATEGORIES,
        yticklabels=CATEGORIES,
        cbar=True,
        annot_kws={"size": 12},  # Font size for annotations
    )
    plt.title("Confusion Matrix")
    plt.ylabel("True label")
    plt.xlabel("Predicted label")
    plt.tight_layout()
    plt.savefig(f"{DIR}/confusion_matrix.png")
    plt.close()

    return cm
