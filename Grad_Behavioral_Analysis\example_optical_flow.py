"""
Example script demonstrating the use of optical flow and advanced preprocessing techniques
for behavioral analysis detection.

This script shows how to:
1. Load video data with optical flow computation
2. Apply advanced preprocessing techniques
3. Create different types of datasets combining frames and optical flow
4. Use temporal preprocessing methods

Author: AI Assistant
Date: 2025-10-03
"""

import numpy as np
import cv2
import matplotlib.pyplot as plt
from helper import (
    load_all_class_frames_with_optical_flow,
    create_optical_flow_dataset,
    extract_optical_flow_features,
    optical_flow_to_hsv,
    optical_flow_magnitude
)
from frames import (
    advanced_preprocess_frame,
    create_multi_channel_frame,
    temporal_difference,
    extract_motion_history,
    adaptive_frame_sampling,
    create_temporal_features
)
from config import (
    CATEGORIES, DATA_PATH, IMG_SIZE, MAX_FRAMES, SKIP,
    OPTICAL_FLOW_FARNEBACK, OPTICAL_FLOW_LUCAS_KANADE, OPTICAL_FLOW_TVL1,
    FLOW_CONCATENATE, FLOW_SEPARATE, FLOW_MAGNITUDE_ONLY, FLOW_FEATURES_ONLY,
    ADVANCED_PROCESSING
)


def demonstrate_optical_flow():
    """Demonstrate optical flow computation and visualization."""
    print("=== Optical Flow Demonstration ===")
    
    # Load a small sample of data with optical flow
    print("Loading frames with optical flow...")
    frames, flows, flow_features, labels = load_all_class_frames_with_optical_flow(
        max_frames=10,  # Small sample for demonstration
        CATEGORIES=CATEGORIES[:1],  # Use only first category
        DATA_PATH=DATA_PATH,
        skip=SKIP,
        flow_method=OPTICAL_FLOW_FARNEBACK
    )
    
    if len(frames) == 0:
        print("No frames loaded. Please check your data path and ensure videos exist.")
        return
    
    print(f"Loaded {len(frames)} frames with optical flow")
    print(f"Frame shape: {frames[0].shape}")
    print(f"Flow shape: {flows[0].shape}")
    print(f"Flow features shape: {flow_features[0].shape}")
    
    # Visualize optical flow for first frame
    if len(flows) > 0:
        flow = flows[0]
        
        # Convert flow to HSV for visualization
        hsv_flow = optical_flow_to_hsv(flow)
        
        # Calculate flow magnitude
        magnitude = optical_flow_magnitude(flow)
        
        # Extract flow features
        features = extract_optical_flow_features(flow)
        print(f"Flow features: {features}")
        
        # Create visualization (if matplotlib is available)
        try:
            fig, axes = plt.subplots(2, 2, figsize=(12, 10))
            
            # Original frame
            axes[0, 0].imshow(frames[0].squeeze(), cmap='gray')
            axes[0, 0].set_title('Original Frame')
            axes[0, 0].axis('off')
            
            # Flow HSV visualization
            axes[0, 1].imshow(cv2.cvtColor(hsv_flow, cv2.COLOR_HSV2RGB))
            axes[0, 1].set_title('Optical Flow (HSV)')
            axes[0, 1].axis('off')
            
            # Flow magnitude
            axes[1, 0].imshow(magnitude, cmap='hot')
            axes[1, 0].set_title('Flow Magnitude')
            axes[1, 0].axis('off')
            
            # Flow vectors (sparse)
            axes[1, 1].imshow(frames[0].squeeze(), cmap='gray')
            h, w = flow.shape[:2]
            y, x = np.mgrid[0:h:10, 0:w:10].reshape(2, -1).astype(int)
            fx, fy = flow[y, x].T
            lines = np.vstack([x, y, x+fx, y+fy]).T.reshape(-1, 2, 2)
            for line in lines:
                axes[1, 1].plot(line[:, 0], line[:, 1], 'r-', linewidth=0.5)
            axes[1, 1].set_title('Flow Vectors')
            axes[1, 1].axis('off')
            
            plt.tight_layout()
            plt.savefig('optical_flow_demo.png', dpi=150, bbox_inches='tight')
            print("Optical flow visualization saved as 'optical_flow_demo.png'")
            
        except Exception as e:
            print(f"Could not create visualization: {e}")


def demonstrate_advanced_preprocessing():
    """Demonstrate advanced preprocessing techniques."""
    print("\n=== Advanced Preprocessing Demonstration ===")
    
    # Create a sample frame (you can replace this with actual video frame)
    sample_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    
    # Apply different preprocessing techniques
    techniques_list = [
        ['grayscale', 'resize', 'normalize'],
        ['grayscale', 'clahe', 'resize', 'normalize'],
        ['grayscale', 'bilateral', 'sharpen', 'resize', 'normalize'],
        ['grayscale', 'stretch', 'edges', 'resize', 'normalize']
    ]
    
    processed_frames = []
    technique_names = [
        'Basic',
        'CLAHE Enhanced',
        'Bilateral + Sharpen',
        'Stretch + Edges'
    ]
    
    for techniques in techniques_list:
        processed = advanced_preprocess_frame(sample_frame, IMG_SIZE, techniques)
        processed_frames.append(processed)
    
    print(f"Applied {len(techniques_list)} different preprocessing pipelines")
    
    # Create multi-channel representations
    multi_channel = create_multi_channel_frame(
        processed_frames[0], 
        channels=['original', 'edges', 'lbp']
    )
    print(f"Multi-channel frame shape: {multi_channel.shape}")


def demonstrate_dataset_creation():
    """Demonstrate different ways to create datasets with optical flow."""
    print("\n=== Dataset Creation Demonstration ===")
    
    # Load sample data
    frames, flows, flow_features, labels = load_all_class_frames_with_optical_flow(
        max_frames=5,
        CATEGORIES=CATEGORIES[:1],
        DATA_PATH=DATA_PATH,
        skip=SKIP,
        flow_method=OPTICAL_FLOW_FARNEBACK
    )
    
    if len(frames) == 0:
        print("No frames loaded for dataset creation demo.")
        return
    
    # Create different types of datasets
    dataset_methods = [
        FLOW_CONCATENATE,
        FLOW_MAGNITUDE_ONLY,
        FLOW_FEATURES_ONLY
    ]
    
    for method in dataset_methods:
        try:
            if method == FLOW_SEPARATE:
                dataset = create_optical_flow_dataset(frames, flows, flow_features, method)
                print(f"{method}: Frames shape: {dataset[0].shape}, Flows shape: {dataset[1].shape}")
            else:
                dataset = create_optical_flow_dataset(frames, flows, flow_features, method)
                print(f"{method}: Dataset shape: {dataset.shape}")
        except Exception as e:
            print(f"Error creating {method} dataset: {e}")


def demonstrate_temporal_processing():
    """Demonstrate temporal preprocessing techniques."""
    print("\n=== Temporal Processing Demonstration ===")
    
    # Create sample temporal sequence
    sequence_length = 10
    sample_frames = np.random.rand(sequence_length, IMG_SIZE, IMG_SIZE).astype(np.float32)
    sample_labels = np.random.randint(0, len(CATEGORIES), sequence_length)
    
    # Temporal difference
    temp_diff = temporal_difference(sample_frames, method='adjacent')
    print(f"Temporal difference shape: {temp_diff.shape}")
    
    # Motion History Image
    mhi = extract_motion_history(sample_frames, duration=5, threshold=30)
    print(f"Motion History Image shape: {mhi.shape}")
    
    # Temporal features
    temp_features = create_temporal_features(sample_frames, feature_type='statistics')
    print(f"Temporal features keys: {list(temp_features.keys())}")
    
    # Adaptive sampling
    sampled_frames, sampled_labels = adaptive_frame_sampling(
        sample_frames, sample_labels, target_frames=5, method='uniform'
    )
    print(f"Sampled frames shape: {sampled_frames.shape}")


def main():
    """Main function to run all demonstrations."""
    print("Behavioral Analysis - Optical Flow and Advanced Preprocessing Demo")
    print("=" * 70)
    
    try:
        # Run demonstrations
        demonstrate_optical_flow()
        demonstrate_advanced_preprocessing()
        demonstrate_dataset_creation()
        demonstrate_temporal_processing()
        
        print("\n" + "=" * 70)
        print("Demo completed successfully!")
        print("\nNext steps:")
        print("1. Integrate optical flow features into your CNN/LSTM models")
        print("2. Experiment with different preprocessing combinations")
        print("3. Use temporal features for improved behavior detection")
        print("4. Consider multi-input models that use both frames and flow")
        
    except Exception as e:
        print(f"Demo failed with error: {e}")
        print("Please check your data path and ensure required dependencies are installed.")


if __name__ == "__main__":
    main()
